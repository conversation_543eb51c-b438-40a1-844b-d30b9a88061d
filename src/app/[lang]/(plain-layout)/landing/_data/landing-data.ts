import type { LandingDataType } from "../types";

export const landingData: LandingDataType = {
  hero: {
    title: "Admin Dashboard Template",
    subtitle: "Modern T3 Stack Admin Dashboard",
    description:
      "A feature-rich template built on T3 Stack, providing beautiful, responsive, and easily customizable user interface for admin applications.",
    primaryCta: "View Demo",
    secondaryCta: "Documentation",
  },
  features: [
    {
      id: "responsive-design",
      title: "Responsive Design",
      description:
        "Modern design optimized for all devices from mobile to desktop",
      icon: "Smartphone",
    },
    {
      id: "i18n-support",
      title: "Multi-language Support",
      description:
        "Built-in i18n system to support multiple languages seamlessly",
      icon: "Globe",
    },
    {
      id: "authentication",
      title: "User Authentication",
      description:
        "Complete authentication system with NextAuth.js, supporting multiple providers",
      icon: "Shield",
    },
    {
      id: "ui-components",
      title: "Reusable UI Components",
      description: "Rich component library built with Shadcn/ui and Radix UI",
      icon: "Layers",
    },
    {
      id: "performance",
      title: "Performance Optimized",
      description: "Using Next.js 13+ with App Router for optimal performance",
      icon: "Zap",
    },
    {
      id: "typescript",
      title: "TypeScript",
      description: "Fully written in TypeScript to ensure type safety",
      icon: "Code",
    },
  ],
  technologies: [
    {
      id: "nextjs",
      name: "Next.js",
      description: "Powerful React framework with App Router",
      icon: "ArrowRight",
      href: "https://nextjs.org",
    },
    {
      id: "typescript",
      name: "TypeScript",
      description: "Programming language with type safety",
      icon: "Code",
      href: "https://www.typescriptlang.org",
    },
    {
      id: "tailwind",
      name: "Tailwind CSS",
      description: "Utility-first CSS framework",
      icon: "Palette",
      href: "https://tailwindcss.com",
    },
    {
      id: "shadcn",
      name: "Shadcn/ui",
      description: "Modern component library",
      icon: "Layers",
      href: "https://ui.shadcn.com",
    },
    {
      id: "nextauth",
      name: "NextAuth.js",
      description: "Complete authentication solution",
      icon: "Shield",
      href: "https://next-auth.js.org",
    },
  ],
  dashboardPreviews: [
    {
      id: "analytics",
      title: "Analytics Dashboard",
      description: "Analytics dashboard with detailed charts and metrics",
      image: "/images/misc/product-01.jpg",
      href: "/dashboards/analytics",
      category: "Dashboard",
    },
    {
      id: "crm",
      title: "CRM Dashboard",
      description: "Efficient customer relationship and sales management",
      image: "/images/misc/product-02.jpg",
      href: "/dashboards/crm",
      category: "Dashboard",
    },
    {
      id: "ecommerce",
      title: "eCommerce Dashboard",
      description: "Comprehensive online store management solution",
      image: "/images/misc/product-03.jpg",
      href: "/dashboards/ecommerce",
      category: "Dashboard",
    },
  ],
  componentShowcase: [
    {
      id: "forms",
      title: "Form Components",
      description: "Diverse and easy-to-use form components",
      category: "UI Components",
      href: "/forms",
    },
    {
      id: "charts",
      title: "Charts & Graphs",
      description: "Interactive charts with various types and styles",
      category: "Data Visualization",
      href: "/charts",
    },
    {
      id: "tables",
      title: "Data Tables",
      description:
        "Data tables with sorting, filtering, and pagination features",
      category: "Data Display",
      href: "/tables",
    },
    {
      id: "cards",
      title: "Card Components",
      description: "Beautiful card components for information display",
      category: "UI Components",
      href: "/cards",
    },
  ],
  testimonials: [
    {
      id: "testimonial-1",
      name: "Alex Johnson",
      role: "Senior Frontend Developer",
      company: "TechCorp Solutions",
      avatar: "/images/avatars/male-01.svg",
      content:
        "This template is absolutely amazing! Saved us tons of development time. The UI components are beautiful and highly customizable.",
      rating: 5,
    },
    {
      id: "testimonial-2",
      name: "Sarah Chen",
      role: "Product Manager",
      company: "StartupXYZ",
      avatar: "/images/avatars/female-01.svg",
      content:
        "Best admin dashboard template I've ever used. The responsive design and performance are truly impressive.",
      rating: 5,
    },
    {
      id: "testimonial-3",
      name: "Michael Rodriguez",
      role: "Full Stack Developer",
      company: "Digital Agency",
      avatar: "/images/avatars/male-02.svg",
      content:
        "TypeScript support and code structure are very professional. Detailed documentation makes customization effortless.",
      rating: 5,
    },
    {
      id: "testimonial-4",
      name: "Emma Thompson",
      role: "UI/UX Designer",
      company: "Design Studio",
      avatar: "/images/avatars/female-02.svg",
      content:
        "Modern and user-friendly design. The components are beautifully crafted and consistent throughout.",
      rating: 5,
    },
    {
      id: "testimonial-5",
      name: "David Kim",
      role: "Tech Lead",
      company: "Enterprise Solutions",
      avatar: "/images/avatars/male-01.svg",
      content:
        "This template helped our team increase development speed by 300%. Highly recommended!",
      rating: 5,
    },
    {
      id: "testimonial-6",
      name: "Lisa Wang",
      role: "Frontend Architect",
      company: "Innovation Labs",
      avatar: "/images/avatars/female-03.svg",
      content:
        "Excellent code quality and optimized performance. This is exactly the template every developer needs.",
      rating: 5,
    },
  ],
};
