import type { LandingDataType } from "../_types";

export const landingData: LandingDataType = {
  hero: {
    title: "Admin Dashboard Template",
    subtitle: "Template hiện đại cho Admin Dashboard",
    description:
      "Một template đầy đủ tính năng được xây dựng trên T3 Stack, cung cấp giao diện người dùng đẹp mắt, responsive và dễ tùy chỉnh cho các ứng dụng quản trị.",
    primaryCta: "Xem Demo",
    secondaryCta: "Tài liệu",
  },
  features: [
    {
      id: "responsive-design",
      title: "Giao diện Responsive",
      description:
        "Thiết kế hiện đại, tối ưu cho mọi thiết bị từ mobile đến desktop",
      icon: "Smartphone",
    },
    {
      id: "i18n-support",
      title: "Hỗ trợ đa ngôn ngữ",
      description:
        "<PERSON><PERSON><PERSON> h<PERSON><PERSON> sẵn hệ thống i18n để hỗ trợ nhiều ngôn ngữ khác nhau",
      icon: "Globe",
    },
    {
      id: "authentication",
      title: "<PERSON><PERSON><PERSON> thực người dùng",
      description:
        "<PERSON><PERSON> thống xác thực hoàn chỉnh với NextAuth.js, hỗ trợ nhiều provider",
      icon: "Shield",
    },
    {
      id: "ui-components",
      title: "UI Components tái sử dụng",
      description:
        "Thư viện component phong phú được xây dựng với Shadcn/ui và Radix UI",
      icon: "Layers",
    },
    {
      id: "performance",
      title: "Tối ưu hiệu năng",
      description: "Sử dụng Next.js 13+ với App Router để đạt hiệu năng tối ưu",
      icon: "Zap",
    },
    {
      id: "typescript",
      title: "TypeScript",
      description: "Được viết hoàn toàn bằng TypeScript để đảm bảo type safety",
      icon: "Code",
    },
  ],
  technologies: [
    {
      id: "nextjs",
      name: "Next.js",
      description: "Framework React mạnh mẽ với App Router",
      icon: "ArrowRight",
      href: "https://nextjs.org",
    },
    {
      id: "typescript",
      name: "TypeScript",
      description: "Ngôn ngữ lập trình với type safety",
      icon: "Code",
      href: "https://www.typescriptlang.org",
    },
    {
      id: "tailwind",
      name: "Tailwind CSS",
      description: "Utility-first CSS framework",
      icon: "Palette",
      href: "https://tailwindcss.com",
    },
    {
      id: "shadcn",
      name: "Shadcn/ui",
      description: "Component library hiện đại",
      icon: "Layers",
      href: "https://ui.shadcn.com",
    },
    {
      id: "nextauth",
      name: "NextAuth.js",
      description: "Giải pháp xác thực hoàn chỉnh",
      icon: "Shield",
      href: "https://next-auth.js.org",
    },
  ],
  dashboardPreviews: [
    {
      id: "analytics",
      title: "Analytics Dashboard",
      description: "Dashboard phân tích với biểu đồ và metrics chi tiết",
      image: "/images/misc/product-01.jpg",
      href: "/dashboards/analytics",
      category: "Dashboard",
    },
    {
      id: "crm",
      title: "CRM Dashboard",
      description: "Quản lý khách hàng và bán hàng hiệu quả",
      image: "/images/misc/product-02.jpg",
      href: "/dashboards/crm",
      category: "Dashboard",
    },
    {
      id: "ecommerce",
      title: "eCommerce Dashboard",
      description: "Quản lý cửa hàng trực tuyến toàn diện",
      image: "/images/misc/product-03.jpg",
      href: "/dashboards/ecommerce",
      category: "Dashboard",
    },
  ],
  componentShowcase: [
    {
      id: "forms",
      title: "Form Components",
      description: "Các component form đa dạng và dễ sử dụng",
      category: "UI Components",
      href: "/forms",
    },
    {
      id: "charts",
      title: "Charts & Graphs",
      description: "Biểu đồ tương tác với nhiều loại khác nhau",
      category: "Data Visualization",
      href: "/charts",
    },
    {
      id: "tables",
      title: "Data Tables",
      description: "Bảng dữ liệu với tính năng sắp xếp, lọc, phân trang",
      category: "Data Display",
      href: "/tables",
    },
    {
      id: "cards",
      title: "Card Components",
      description: "Các loại card đẹp mắt cho hiển thị thông tin",
      category: "UI Components",
      href: "/cards",
    },
  ],
};
