"use client";

import { useLandingData } from "../_hooks/use-landing-data";
import {
  ComponentShowcaseSection,
  DashboardPreviewSection,
  FeaturesSection,
  FooterSection,
  HeroSection,
  TechnologySection,
  TestimonialsSection,
} from "./index";

export function LandingContent() {
  const { data, loading, error } = useLandingData();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-destructive mb-2">Error</h2>
          <p className="text-muted-foreground">
            {error || "Failed to load landing page data"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen">
      <HeroSection {...data.hero} />
      <FeaturesSection features={data.features} />
      <TechnologySection technologies={data.technologies} />
      <DashboardPreviewSection dashboards={data.dashboardPreviews} />
      <ComponentShowcaseSection components={data.componentShowcase} />
      <TestimonialsSection testimonials={data.testimonials} />
      <FooterSection />
    </main>
  );
}
