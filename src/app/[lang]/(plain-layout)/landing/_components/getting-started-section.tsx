'use client';

import { Copy, Check, Download, GitBranch, Play } from 'lucide-react';
import { useState } from 'react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';

export function GettingStartedSection() {
  const [copiedStep, setCopiedStep] = useState<number | null>(null);

  const steps = [
    {
      id: 1,
      title: "Clone Repository",
      description: "Sao chép repository về máy local của bạn",
      command: "git clone https://github.com/your-repo/admin-dashboard-template.git",
      icon: GitBranch
    },
    {
      id: 2,
      title: "Install Dependencies",
      description: "Cài đặt các package dependencies cần thiết",
      command: "cd admin-dashboard-template && npm install",
      icon: Download
    },
    {
      id: 3,
      title: "Start Development",
      description: "Khởi chạy server development và bắt đầu phát triển",
      command: "npm run dev",
      icon: Play
    }
  ];

  const copyToClipboard = async (text: string, stepId: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStep(stepId);
      setTimeout(() => setCopiedStep(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Bắt đầu nhanh chóng
          </h2>
          <p className="text-lg text-muted-foreground">
            Chỉ với 3 bước đơn giản, bạn đã có thể bắt đầu xây dựng ứng dụng admin của mình
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="grid gap-6">
            {steps.map((step, index) => {
              const IconComponent = step.icon;
              const isLastStep = index === steps.length - 1;
              
              return (
                <div key={step.id} className="relative">
                  <Card className="group hover:shadow-lg transition-all duration-300">
                    <CardHeader className="flex flex-row items-center space-y-0 pb-4">
                      <div className="flex items-center mr-4">
                        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary text-primary-foreground font-bold text-lg mr-4">
                          {step.id}
                        </div>
                        <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-5 w-5 text-primary" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-xl mb-1">{step.title}</CardTitle>
                        <CardDescription className="text-base">
                          {step.description}
                        </CardDescription>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="bg-muted/50 rounded-lg p-4 font-mono text-sm relative group/code">
                        <code className="text-foreground">{step.command}</code>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="absolute right-2 top-2 h-8 w-8 p-0 opacity-0 group-hover/code:opacity-100 transition-opacity"
                          onClick={() => copyToClipboard(step.command, step.id)}
                        >
                          {copiedStep === step.id ? (
                            <Check className="h-3 w-3 text-green-500" />
                          ) : (
                            <Copy className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Connection Line */}
                  {!isLastStep && (
                    <div className="absolute left-9 top-full w-0.5 h-6 bg-border z-10" />
                  )}
                </div>
              );
            })}
          </div>

          {/* Additional Info */}
          <div className="mt-12 text-center space-y-4">
            <div className="flex flex-wrap justify-center gap-2">
              <Badge variant="secondary">Node.js 18+</Badge>
              <Badge variant="secondary">npm/yarn/pnpm</Badge>
              <Badge variant="secondary">Git</Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Yêu cầu hệ thống: Node.js 18 trở lên, npm/yarn/pnpm và Git
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
