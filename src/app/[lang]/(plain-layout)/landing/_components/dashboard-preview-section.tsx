"use client";

import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { landingData } from "../_data/landing-data";

export function DashboardPreviewSection() {
  const { dashboardPreviews: dashboards } = landingData;
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Dashboard Templates
          </h2>
          <p className="text-lg text-muted-foreground">
            Explore pre-designed dashboard templates for various industries and
            use cases
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {dashboards.map((dashboard, index) => (
            <Card
              key={dashboard.id}
              className={`group hover:shadow-xl transition-all duration-500 overflow-hidden ${
                index === 1 ? "lg:scale-105" : ""
              }`}
            >
              <div className="relative overflow-hidden">
                <Image
                  src={dashboard.image}
                  alt={dashboard.title}
                  width={400}
                  height={250}
                  className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <Badge className="absolute top-4 left-4 bg-primary/90 backdrop-blur-sm">
                  {dashboard.category}
                </Badge>
              </div>

              <CardHeader>
                <CardTitle className="text-xl group-hover:text-primary transition-colors">
                  {dashboard.title}
                </CardTitle>
                <CardDescription className="text-base">
                  {dashboard.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                <Button className="w-full group/btn" asChild>
                  <Link href={dashboard.href}>
                    View Demo
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button size="lg" variant="outline" asChild>
            <Link href="/dashboards">
              View All Dashboards
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
