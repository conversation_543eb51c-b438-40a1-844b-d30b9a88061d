'use client';

import Link from 'next/link';
import { ExternalLink } from 'lucide-react';

import { DynamicIcon } from '~/components/dynamic-icon';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import type { TechnologyType } from '../_types';

interface TechnologySectionProps {
  technologies: TechnologyType[];
}

export function TechnologySection({ technologies }: TechnologySectionProps) {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Công nghệ hiện đại
          </h2>
          <p className="text-lg text-muted-foreground">
            Đ<PERSON><PERSON>c xây dựng trên nền tảng các công nghệ tiên tiến và đáng tin cậy nhất
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {technologies.map((tech) => (
            <Card key={tech.id} className="group hover:shadow-lg transition-all duration-300 hover:scale-105">
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 p-4 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5 w-fit group-hover:from-primary/20 group-hover:to-primary/10 transition-all">
                  <DynamicIcon 
                    name={tech.icon} 
                    className="h-8 w-8 text-primary" 
                  />
                </div>
                <CardTitle className="text-xl">{tech.name}</CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <CardDescription className="text-base">
                  {tech.description}
                </CardDescription>
                <Button variant="outline" size="sm" className="group/btn" asChild>
                  <Link href={tech.href} target="_blank" rel="noopener noreferrer">
                    Tìm hiểu thêm
                    <ExternalLink className="ml-2 h-3 w-3 transition-transform group-hover/btn:translate-x-0.5" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
