'use client';

import Link from 'next/link';
import { ArrowR<PERSON>, Layers, BarChart3, Table, CreditCard } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import type { ComponentShowcaseType } from '../_types';

interface ComponentShowcaseSectionProps {
  components: ComponentShowcaseType[];
}

const iconMap = {
  'forms': Layers,
  'charts': BarChart3,
  'tables': Table,
  'cards': CreditCard,
};

export function ComponentShowcaseSection({ components }: ComponentShowcaseSectionProps) {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            UI Components Library
          </h2>
          <p className="text-lg text-muted-foreground">
            Thư viện component phong phú được xây dựng với Shadcn/ui và Radix UI
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          {components.map((component) => {
            const IconComponent = iconMap[component.id as keyof typeof iconMap] || Layers;
            
            return (
              <Card key={component.id} className="group hover:shadow-lg transition-all duration-300 hover:border-primary/20">
                <CardHeader className="flex flex-row items-center space-y-0 pb-4">
                  <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors mr-4">
                    <IconComponent className="h-6 w-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <CardTitle className="text-lg">{component.title}</CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        {component.category}
                      </Badge>
                    </div>
                    <CardDescription>
                      {component.description}
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <Button variant="outline" className="group/btn" asChild>
                    <Link href={component.href}>
                      Xem Components
                      <ArrowRight className="ml-2 h-3 w-3 transition-transform group-hover/btn:translate-x-0.5" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Component Stats */}
        <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-primary mb-2">50+</div>
              <div className="text-sm text-muted-foreground">UI Components</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">15+</div>
              <div className="text-sm text-muted-foreground">Chart Types</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">20+</div>
              <div className="text-sm text-muted-foreground">Form Elements</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">100%</div>
              <div className="text-sm text-muted-foreground">Accessible</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
