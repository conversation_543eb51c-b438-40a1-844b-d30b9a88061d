import type { DynamicIconNameType } from "~/types";

export interface FeatureType {
  id: string;
  title: string;
  description: string;
  icon: DynamicIconNameType;
}

export interface TechnologyType {
  id: string;
  name: string;
  description: string;
  icon: DynamicIconNameType;
  href: string;
}

export interface DashboardPreviewType {
  id: string;
  title: string;
  description: string;
  image: string;
  href: string;
  category: string;
}

export interface ComponentShowcaseType {
  id: string;
  title: string;
  description: string;
  category: string;
  href: string;
}

export interface LandingDataType {
  hero: {
    title: string;
    subtitle: string;
    description: string;
    primaryCta: string;
    secondaryCta: string;
  };
  features: FeatureType[];
  technologies: TechnologyType[];
  dashboardPreviews: DashboardPreviewType[];
  componentShowcase: ComponentShowcaseType[];
}
