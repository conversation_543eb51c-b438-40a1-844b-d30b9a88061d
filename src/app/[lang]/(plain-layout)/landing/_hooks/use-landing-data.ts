'use client';

import { useState, useEffect } from 'react';
import type { LandingDataType } from '../_types';
import { landingData } from '../_data/landing-data';

export function useLandingData() {
  const [data, setData] = useState<LandingDataType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Simulate API call delay
      const timer = setTimeout(() => {
        setData(landingData);
        setLoading(false);
      }, 100);

      return () => clearTimeout(timer);
    } catch (err) {
      setError('Failed to load landing data');
      setLoading(false);
    }
  }, []);

  return { data, loading, error };
}
