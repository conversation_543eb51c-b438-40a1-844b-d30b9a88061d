# Landing Page - Admin Dashboard Template

<PERSON><PERSON> landing hoàn chỉnh cho Admin Dashboard Template <PERSON><PERSON><PERSON><PERSON> xây dựng theo quy tắc tách biệt logic và giao diện.

## 🏗 Cấu trúc

```
landing/
├── page.tsx                    # Entry point
├── _components/                # UI components
│   ├── hero-section.tsx       # Hero section với CTA
│   ├── features-section.tsx   # Tính năng nổi bật
│   ├── technology-section.tsx # Công nghệ sử dụng
│   ├── dashboard-preview-section.tsx # Preview dashboards
│   ├── component-showcase-section.tsx # Showcase UI components
│   ├── getting-started-section.tsx # Hướng dẫn bắt đầu
│   ├── footer-section.tsx     # Footer
│   ├── landing-content.tsx    # Main content wrapper
│   └── index.ts               # Export all components
├── _hooks/                     # Logic hooks
│   └── use-landing-data.ts    # Data management hook
├── _data/                      # Static data
│   └── landing-data.ts        # Landing page data
├── _types/                     # TypeScript types
│   └── index.ts               # Type definitions
└── README.md                   # Documentation
```

## 🎨 Sections

### 1. Hero Section
- Tiêu đề chính và mô tả
- CTA buttons (<PERSON>em Demo, Tài liệu)
- Statistics (50+ components, 3 dashboards, etc.)
- Background pattern với gradient

### 2. Features Section
- 6 tính năng chính:
  - Giao diện Responsive
  - Hỗ trợ đa ngôn ngữ
  - Xác thực người dùng
  - UI Components tái sử dụng
  - Tối ưu hiệu năng
  - TypeScript

### 3. Technology Section
- 5 công nghệ chính:
  - Next.js
  - TypeScript
  - Tailwind CSS
  - Shadcn/ui
  - NextAuth.js

### 4. Dashboard Preview Section
- 3 dashboard templates:
  - Analytics Dashboard
  - CRM Dashboard
  - eCommerce Dashboard

### 5. Component Showcase Section
- 4 loại components:
  - Form Components
  - Charts & Graphs
  - Data Tables
  - Card Components

### 6. Getting Started Section
- 3 bước cài đặt:
  - Clone Repository
  - Install Dependencies
  - Start Development
- Copy-to-clipboard functionality

### 7. Footer Section
- Links tổ chức theo categories
- Social media links
- Copyright và legal links

## 🔧 Tùy chỉnh

### Thay đổi nội dung
Chỉnh sửa file `_data/landing-data.ts`:

```typescript
export const landingData: LandingDataType = {
  hero: {
    title: "Your Title",
    subtitle: "Your Subtitle",
    // ...
  },
  // ...
}
```

### Thêm section mới
1. Tạo component trong `_components/`
2. Import và sử dụng trong `landing-content.tsx`
3. Cập nhật types nếu cần

### Styling
- Sử dụng Tailwind CSS classes
- Background pattern được định nghĩa trong `globals.css`
- Responsive design với breakpoints chuẩn

## 📱 Responsive Design

- **Mobile**: Single column layout
- **Tablet**: 2 columns cho cards
- **Desktop**: 3 columns cho cards, full layout

## 🎯 Performance

- Server Components mặc định
- Client Components chỉ khi cần thiết
- Image optimization với Next.js Image
- Lazy loading cho sections

## 🔗 Navigation

Trang landing có thể truy cập tại: `/landing`

## 🚀 Deployment

Trang landing sẽ được build cùng với toàn bộ ứng dụng khi chạy:

```bash
npm run build
```

## 📝 Notes

- Tuân thủ quy tắc tách biệt logic và UI
- Sử dụng shadcn-ui components
- State management với custom hooks
- TypeScript types đầy đủ
- Responsive design
