import type { <PERSON>ada<PERSON> } from "next";
import { ComponentShowcaseSection } from "./_components/component-showcase-section";
import { DashboardPreviewSection } from "./_components/dashboard-preview-section";
import { FeaturesSection } from "./_components/features-section";
import { FooterSection } from "./_components/footer-section";
import { HeroSection } from "./_components/hero-section";
import { TechnologySection } from "./_components/technology-section";
import { TestimonialsSection } from "./_components/testimonials-section";

// Define metadata for the page
export const metadata: Metadata = {
  title: "Landing",
  description:
    "Modern admin dashboard template, providing beautiful, responsive, and easily customizable user interface for admin applications.",
  keywords: [
    "admin dashboard",
    "template",
    "next.js",
    "typescript",
    "tailwind css",
    "shadcn ui",
    "t3 stack",
    "react",
    "responsive design",
  ],
  authors: [{ name: "Admin Dashboard Template Team" }],
  openGraph: {
    title: "Landing",
    description:
      "Modern admin dashboard template, providing beautiful, responsive, and easily customizable user interface for admin applications.",
    type: "website",
    locale: "en_US",
    siteName: "Admin Dashboard Template",
  },
  twitter: {
    card: "summary_large_image",
    title: "Landing",
    description:
      "Modern admin dashboard template, providing beautiful, responsive, and easily customizable user interface for admin applications.",
  },
};

export default function LandingPage() {
  return (
    <main className="min-h-screen">
      <HeroSection />
      <FeaturesSection />
      <TechnologySection />
      <DashboardPreviewSection />
      <ComponentShowcaseSection />
      <TestimonialsSection />
      <FooterSection />
    </main>
  );
}
