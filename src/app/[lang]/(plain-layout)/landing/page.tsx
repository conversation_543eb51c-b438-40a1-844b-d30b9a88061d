import type { Metadata } from 'next';

import { LandingContent } from './_components/landing-content';

// Define metadata for the page
export const metadata: Metadata = {
  title: 'Admin Dashboard Template - Modern T3 Stack Template',
  description: 'Template hiện đại cho admin dashboard được xây dựng trên T3 Stack, cung cấp giao diện người dùng đẹp mắt và dễ tùy chỉnh.',
  keywords: [
    'admin dashboard',
    'template',
    'next.js',
    'typescript',
    'tailwind css',
    'shadcn ui',
    't3 stack',
    'react',
    'responsive design'
  ],
  authors: [{ name: 'Admin Dashboard Template Team' }],
  openGraph: {
    title: 'Admin Dashboard Template - Modern T3 Stack Template',
    description: 'Template hiện đại cho admin dashboard được xây dựng trên T3 Stack, cung cấp giao diện người dùng đẹp mắt và dễ tùy chỉnh.',
    type: 'website',
    locale: 'vi_VN',
    siteName: 'Admin Dashboard Template'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Admin Dashboard Template - Modern T3 Stack Template',
    description: 'Template hiện đại cho admin dashboard được xây dựng trên T3 Stack, cung cấp giao diện người dùng đẹp mắt và dễ tùy chỉnh.'
  }
};

export default function LandingPage() {
  return <LandingContent />;
}
