@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";
@import "./themes.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --font-lato: var(--font-lato), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-cairo: var(--font-cairo), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);

  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);

  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-xl: calc(var(--radius) + 4px);
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-collapsible-down: collapsible-down 0.2s ease-out;
  --animate-collapsible-up: collapsible-up 0.2s ease-out;
  --animate-collapsible-right: collapsible-right 0.2s ease-out;
  --animate-collapsible-left: collapsible-left 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes collapsible-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }
  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes collapsible-right {
    from {
      width: 0;
    }
    to {
      width: var(--radix-collapsible-content-width);
    }
  }
  @keyframes collapsible-left {
    from {
      width: var(--radix-collapsible-content-width);
    }
    to {
      width: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 1rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-border, currentColor);
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.01 260);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.01 260);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.01 260);
  --primary: oklch(0.25 0.01 260);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.95 0 0);
  --secondary-foreground: oklch(0.25 0.01 260);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.55 0.01 260);
  --accent: oklch(0.95 0 0);
  --accent-foreground: oklch(0.25 0.01 260);
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: oklch(0.98 0 0);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0 0);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0 0);
  --success: oklch(0.55 0.2 145);
  --success-foreground: oklch(0.98 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(0.9 0 0);
  --ring: oklch(0.15 0.01 260);
  --radius: 0.5rem;
  --chart-1: oklch(0.6 0.18 260);
  --chart-2: oklch(0.6 0.18 145);
  --chart-3: oklch(0.7 0.2 60);
  --chart-4: oklch(0.65 0.25 290);
  --chart-5: oklch(0.65 0.25 25);
  --sidebar-background: var(--background);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);

  /* Calendar vars */
  --fc-small-font-size: 0.875em;
  --fc-page-bg-color: var(--border);
  --fc-neutral-bg-color: var(--border);
  --fc-neutral-text-color: var(--accent-foreground);
  --fc-border-color: var(--border);
  --fc-button-text-color: var(--background);
  --fc-button-bg-color: var(--primary);
  --fc-button-border-color: var(--primary);
  --fc-button-hover-bg-color: var(--primary) / 0.8;
  --fc-button-hover-border-color: var(--primary);
  --fc-button-active-bg-color: var(--primary) / 0.8;
  --fc-button-active-border-color: var(--primary) / 0;
  --fc-event-bg-color: var(--primary);
  --fc-event-border-color: var(--primary);
  --fc-event-text-color: var(--background);
  --fc-event-selected-overlay-color: var(--muted);
  --fc-more-link-bg-color: var(--muted);
  --fc-more-link-text-color: inherit;
  --fc-event-resizer-thickness: 8px;
  --fc-event-resizer-dot-total-width: 8px;
  --fc-event-resizer-dot-border-width: var(--radius);
  --fc-non-business-color: rgba(215, 215, 215, 0.3);
  --fc-bg-event-color: var(--success);
  --fc-bg-event-opacity: 0.3;
  --fc-highlight-color: rgba(188, 232, 241, 0.3);
  --fc-today-bg-color: var(--primary) / 0.15;
  --fc-now-indicator-color: var(--destructive);
  --sidebar: 0 0% 98%;
}
.dark {
  --background: oklch(0.15 0.01 260);
  --foreground: oklch(0.98 0 0);
  --card: oklch(0.15 0.01 260);
  --card-foreground: oklch(0.98 0 0);
  --popover: oklch(0.15 0.01 260);
  --popover-foreground: oklch(0.98 0 0);
  --primary: oklch(0.98 0 0);
  --primary-foreground: oklch(0.25 0.01 260);
  --secondary: oklch(0.25 0.01 260);
  --secondary-foreground: oklch(0.98 0 0);
  --muted: oklch(0.25 0.01 260);
  --muted-foreground: oklch(0.65 0.01 260);
  --accent: oklch(0.25 0.01 260);
  --accent-foreground: oklch(0.98 0 0);
  --destructive: oklch(0.5 0.25 25);
  --destructive-foreground: oklch(0.98 0 0);
  --success: oklch(0.5 0.2 145);
  --success-foreground: oklch(0.98 0 0);
  --border: oklch(0.25 0.01 260);
  --input: oklch(0.25 0.01 260);
  --ring: oklch(0.8 0.01 260);
  --chart-1: oklch(0.6 0.18 260);
  --chart-2: oklch(0.6 0.18 145);
  --chart-3: oklch(0.7 0.2 60);
  --chart-4: oklch(0.65 0.25 290);
  --chart-5: oklch(0.65 0.25 25);
  --sidebar-background: var(--background);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

aside.EmojiPickerReact {
  /* Emoji Picker vars */
  --epr-emoji-size: 2rem;
  --epr-emoji-padding: 0.25rem;
  --epr-emoji-gap: 0.5rem;
  --epr-hover-bg-color: var(--accent);
  --epr-focus-bg-color: var(--accent);
  --epr-bg-color: var(--background);
  --epr-reactions-bg-color: var(--background);
  --epr-highlight-color: var(--primary);
  --epr-category-icon-active-color: var(--primary);
  --epr-category-label-bg-color: var(--background);
  --epr-text-color: var(--foreground);
  --epr-horizontal-padding: 0.75rem;
  --epr-search-input-bg-color: var(--muted);
  --epr-search-input-border-radius: var(--radius);
  --epr-search-input-padding: 0 40px;
  --epr-preview-text-size: 0.875rem;
  --epr-header-padding: var(--epr-horizontal-padding)
    var(--epr-horizontal-padding);
  --epr-picker-border-radius: var(--radius);
  --epr-picker-border-color: var(--border);
  --epr-skin-tone-outer-border-color: var(--border);
}
.EmojiPickerReact .epr-btn {
  border-radius: var(--radius);
}
.EmojiPickerReact .epr-btn:focus::before {
  border-radius: var(--radius);
  border-width: 1px;
}

/* Landing page background pattern */
.bg-grid-pattern {
  background-image: radial-gradient(
    circle at 1px 1px,
    rgba(255, 255, 255, 0.15) 1px,
    transparent 0
  );
  background-size: 20px 20px;
}

.dark .bg-grid-pattern {
  background-image: radial-gradient(
    circle at 1px 1px,
    rgba(255, 255, 255, 0.1) 1px,
    transparent 0
  );
}
