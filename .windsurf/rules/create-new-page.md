---
trigger: model_decision
description: Quy tắc tạo page mới với tách biệt logic và giao diện
---

# Quy tắc tạo Page mới

## Nguyên tắc bắt buộc

1. **TÁCH BIỆT**: Logic riêng, UI riêng, Data riêng
2. **CHIA NHỎ**: Component nhỏ, tái sử dụng được
3. **SHADCN-UI**: Ưu tiên components từ `~/components/ui/`
4. **TYPESCRIPT**: Types đầy đủ, import từ file riêng

## Cấu trúc bắt buộc

```
PAGE_NAME/
├── page.tsx              # Entry point
├── types.ts              # TypeScript interfaces (file riêng)
├── _components/          # UI components
│   ├── main-component.tsx
│   ├── sub-component.tsx
│   └── nested/           # Sub-folder nếu cần
└── _data/                # Static data (nếu có)
```

## Quy trình tạo

### 1. Tạo types.ts

```typescript
// types.ts
export interface PageDataType {
  id: string;
  name: string;
  value: number;
}
```

### 2. Tạo components

```typescript
// _components/main-component.tsx
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import type { PageDataType } from "../types";

interface Props {
  data: PageDataType[];
}

export function MainComponent({ data }: Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Title</CardTitle>
      </CardHeader>
      <CardContent>{/* Content */}</CardContent>
    </Card>
  );
}
```

### 3. Tạo page.tsx

```typescript
// page.tsx
import type { Metadata } from "next";
import { MainComponent } from "./_components/main-component";

export const metadata: Metadata = {
  title: "Page Title",
};

export default function PageName() {
  return (
    <section className="container p-4">
      <MainComponent data={[]} />
    </section>
  );
}
```

## Patterns bắt buộc

### ✅ LUÔN LÀM

- Types trong file `types.ts` riêng
- Components chia nhỏ, tái sử dụng
- Sử dụng shadcn-ui components
- Import data từ `_data/` (nếu có)
- Metadata đầy đủ

### ❌ KHÔNG BAO GIỜ

- Logic trực tiếp trong component
- Hard code data trong JSX
- Component quá lớn
- Thiếu TypeScript types

## Checklist

- [ ] `types.ts` file riêng
- [ ] Components trong `_components/`
- [ ] Shadcn-ui components
- [ ] Metadata trong page.tsx
- [ ] Responsive design

**Nhớ: Tách biệt, chia nhỏ, tái sử dụng!**
