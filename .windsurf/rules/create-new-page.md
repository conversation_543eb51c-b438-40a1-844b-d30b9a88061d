---
trigger: model_decision
description: Quy tắc tạo page mới với tách biệt logic và giao diện
---

# Quy tắc tạo Page mới

## Nguyên tắc bắt buộc

1. **KHÔNG** để logic và giao diện chung 1 file
2. **LUÔN** chia nhỏ component để tái sử dụng
3. **ƯU TIÊN** sử dụng components từ `@/components/ui/`
4. **KHÔNG** hard code danh sách - dùng state management

## Quy trình tạo page

### Bước 1: Tạo cấu trúc thư mục

```
src/app/[lang]/(dashboard-layout)/dashboards/PAGE_NAME/
├── page.tsx              # Page entry point với layout grid
├── types.ts              # TypeScript types (file riêng)
├── _components/          # UI components
│   ├── component-name.tsx
│   ├── component-name-chart.tsx
│   ├── component-name-table.tsx
│   └── overview/         # Sub-components cho overview
│       ├── index.tsx
│       ├── metric-1.tsx
│       ├── metric-2.tsx
│       └── metric-chart.tsx
└── _data/                # Static data files
    ├── component-data.ts
    ├── overview.ts
    └── other-data.ts
```

### Bước 2: Tạo types trước

```typescript
// types.ts (file riêng, không trong _types.ts)
import type { DynamicIconNameType } from "~/types";

export interface MetricType {
  averageValue: number;
  percentageChange: number;
  perMonth: Array<{ month: string; value: number; fill?: string }>;
}

export interface OverviewType {
  uniqueVisitors: MetricType;
  averageSessionDuration: MetricType;
  bounceRate: MetricType;
  conversionRate: MetricType;
}

export interface ComponentDataType {
  period: string;
  items: Array<{
    name: string;
    value: number;
    fill: string;
    percentageChange: number;
    icon: DynamicIconNameType;
  }>;
}
```

### Bước 3: Tạo data files

```typescript
// _data/overview.ts
import type { OverviewType } from "../types";

export const overviewData: OverviewType = {
  uniqueVisitors: {
    averageValue: 15091.67,
    percentageChange: 0.07,
    perMonth: [
      { month: "January", value: 12000 },
      { month: "February", value: 16000 },
      // ...
    ],
  },
  // ... other metrics
};
```

```typescript
// _data/component-data.ts
import type { ComponentDataType } from "../types";

export const componentData: ComponentDataType = {
  period: "Last 30 days",
  items: [
    {
      name: "Item 1",
      value: 1000,
      fill: "var(--chart-1)",
      percentageChange: 0.05,
      icon: "TrendingUp",
    },
    // ...
  ],
};
```

### Bước 4: Tạo overview components (nếu có)

```typescript
// _components/overview/index.tsx
import { overviewData } from "../../_data/overview";

import { UniqueVisitors } from "./unique-visitors";
import { AverageSessionDuration } from "./average-session-duration";
import { BounceRate } from "./bounce-rate";
import { ConversionRate } from "./conversion-rate";

export function Overview() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:col-span-full md:grid-cols-4">
      <UniqueVisitors data={overviewData.uniqueVisitors} />
      <AverageSessionDuration data={overviewData.averageSessionDuration} />
      <BounceRate data={overviewData.bounceRate} />
      <ConversionRate data={overviewData.conversionRate} />
    </div>
  );
}
```

```typescript
// _components/overview/unique-visitors.tsx
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { UniqueVisitorsChart } from "./unique-visitors-chart";
import type { MetricType } from "../../types";

interface UniqueVisitorsProps {
  data: MetricType;
}

export function UniqueVisitors({ data }: UniqueVisitorsProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Unique Visitors</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {data.averageValue.toLocaleString()}
        </div>
        <p className="text-xs text-muted-foreground">
          {data.percentageChange > 0 ? "+" : ""}
          {(data.percentageChange * 100).toFixed(1)}% from last month
        </p>
        <UniqueVisitorsChart data={data.perMonth} />
      </CardContent>
    </Card>
  );
}
```

### Bước 5: Tạo main components

```typescript
// _components/traffic-sources.tsx
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { TrafficSourcesChart } from "./traffic-sources-chart";
import { TrafficSourcesTable } from "./traffic-sources-table";
import { trafficSourcesData } from "../_data/traffic-sources";

export function TrafficSources() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Traffic Sources</CardTitle>
        <CardDescription>
          Showing traffic sources for {trafficSourcesData.period}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <TrafficSourcesChart data={trafficSourcesData.sources} />
        <TrafficSourcesTable data={trafficSourcesData.sources} />
      </CardContent>
    </Card>
  );
}
```

### Bước 6: Tạo entry point với grid layout

```typescript
// page.tsx
import type { Metadata } from "next";

import { ConversionFunnel } from "./_components/conversion-funnel";
import { EngagementByDevice } from "./_components/engagement-by-device";
import { NewVsReturningVisitors } from "./_components/new-vs-returning-visitors";
import { Overview } from "./_components/overview";
import { PerformanceOverTime } from "./_components/performance-over-time";
import { TrafficSources } from "./_components/traffic-sources";
import { VisitorsByCountry } from "./_components/visitors-by-country";

export const metadata: Metadata = {
  title: "Dashboard Name",
};

export default function DashboardPage() {
  return (
    <section className="container grid gap-4 p-4 md:grid-cols-2">
      <Overview />
      <TrafficSources />
      <div className="space-y-4">
        <ConversionFunnel />
        <NewVsReturningVisitors />
      </div>
      <PerformanceOverTime />
      <VisitorsByCountry />
      <EngagementByDevice />
    </section>
  );
}
```

## Patterns bắt buộc

### 1. Dashboard Structure

- ✅ Data import từ `_data/` files
- ✅ Types import từ `types.ts` (file riêng)
- ✅ Component chia theo chức năng (chart, table, overview)
- ❌ Logic trực tiếp trong component

### 2. Component Hierarchy

- ✅ Main component (e.g., TrafficSources)
- ✅ Chart component (e.g., TrafficSourcesChart)
- ✅ Table component (e.g., TrafficSourcesTable)
- ✅ Overview sub-components trong thư mục riêng
- ❌ Component quá lớn, nhiều chức năng

### 3. Data Management

- ✅ Static data trong `_data/` files
- ✅ Import data vào component
- ✅ TypeScript interfaces đầy đủ
- ❌ Hard code data trong JSX

## Checklist bắt buộc

- [ ] Types trong file `types.ts` riêng
- [ ] Data trong `_data/` files
- [ ] Component chia theo chức năng (main, chart, table)
- [ ] Overview components trong sub-folder
- [ ] Sử dụng shadcn-ui components
- [ ] Grid layout trong page.tsx
- [ ] Import data vào component
- [ ] TypeScript interfaces đầy đủ
- [ ] Responsive với Tailwind

## Ví dụ cấu trúc hoàn chỉnh (Analytics Pattern)

```
analytics/
├── page.tsx                    # Grid layout với components
├── types.ts                    # All TypeScript interfaces
├── _components/
│   ├── overview/               # Overview metrics
│   │   ├── index.tsx          # Main overview component
│   │   ├── unique-visitors.tsx
│   │   ├── unique-visitors-chart.tsx
│   │   ├── bounce-rate.tsx
│   │   └── bounce-rate-chart.tsx
│   ├── traffic-sources.tsx     # Main component
│   ├── traffic-sources-chart.tsx
│   ├── traffic-sources-table.tsx
│   ├── conversion-funnel.tsx
│   ├── conversion-funnel-chart.tsx
│   └── performance-over-time.tsx
└── _data/
    ├── overview.ts             # Overview metrics data
    ├── traffic-sources.ts      # Traffic data
    ├── conversion-funnel.ts    # Funnel data
    └── performance-over-time.ts
```

Luôn nhớ: **Data riêng, Types riêng, Components chia nhỏ!**
