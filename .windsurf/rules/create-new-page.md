---
trigger: model_decision
description: Quy tắc tạo page mới với tách biệt logic và giao diện
---

# Quy tắc tạo Page mới

## Nguyên tắc bắt buộc

1. **KHÔNG** để logic và giao diện chung 1 file
2. **LUÔN** chia nhỏ component để tái sử dụng
3. **ƯU TIÊN** sử dụng shadcn-ui components từ `@/components/ui/`
4. **KHÔNG** hard code danh sách - dùng state management

## Quy trình tạo page

### Bước 1: Tạo cấu trúc thư mục
```
src/app/[lang]/(dashboard-layout)/PAGE_NAME/
├── page.tsx              # Entry point
├── _components/          # UI components
├── _hooks/              # Logic hooks
├── _lib/                # API, validation
└── _types/              # TypeScript types
```

### Bước 2: Tạo types trước
```typescript
// _types/index.ts
export interface PageData {
  id: string;
  name: string;
  createdAt: Date;
}
```

### Bước 3: Tạo API functions
```typescript
// _lib/api.ts
export async function fetchData(): Promise<PageData[]> {}
export async function createItem(data: Omit<PageData, 'id'>): Promise<PageData> {}
export async function updateItem(id: string, data: Partial<PageData>): Promise<PageData> {}
export async function deleteItem(id: string): Promise<void> {}
```

### Bước 4: Tạo custom hook (Logic)
```typescript
// _hooks/use-page-data.ts
'use client';
import { useState } from 'react';

export function usePageData() {
  const [data, setData] = useState<PageData[]>([]);
  const [loading, setLoading] = useState(true);

  const addItem = (item: PageData) => setData(prev => [...prev, item]);
  const updateItem = (id: string, updates: Partial<PageData>) => 
    setData(prev => prev.map(item => 
      item.id === id ? {...item, ...updates} : item
    ));
  const removeItem = (id: string) => 
    setData(prev => prev.filter(item => item.id !== id));

  return { data, loading, addItem, updateItem, removeItem };
}
```

### Bước 5: Tạo UI components
```typescript
// _components/page-content.tsx
'use client';
import { usePageData } from '../_hooks';
import { Button } from '@/components/ui/button';
import { DataTable } from './data-table';

export function PageContent() {
  const { data, loading, addItem } = usePageData();
  
  if (loading) return <div>Loading...</div>;
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <h1>Page Title</h1>
        <Button onClick={() => addItem(newItem)}>Add</Button>
      </div>
      <DataTable data={data} />
    </div>
  );
}
```

### Bước 6: Tạo entry point
```typescript
// page.tsx
import { PageContent } from './_components/page-content';

export default function PageName() {
  return <PageContent />;
}
```

## Patterns bắt buộc

### 1. Tách biệt Logic
- ✅ Logic trong custom hooks
- ✅ API calls trong `_lib/api.ts`
- ✅ Validation trong `_lib/validations.ts`
- ❌ Logic trực tiếp trong component

### 2. Component Structure
- ✅ Chia nhỏ thành nhiều component
- ✅ Mỗi component có 1 responsibility
- ✅ Sử dụng shadcn-ui làm base
- ❌ Component quá lớn, nhiều chức năng

### 3. State Management
- ✅ useState/useReducer cho danh sách
- ✅ CRUD operations đầy đủ
- ✅ Loading và error states
- ❌ Hard code data trong JSX

## Checklist bắt buộc

- [ ] Logic tách riêng vào hooks
- [ ] Component chia nhỏ, tái sử dụng
- [ ] Sử dụng shadcn-ui components
- [ ] State management cho danh sách
- [ ] Loading/error states
- [ ] TypeScript types đầy đủ
- [ ] API functions riêng biệt
- [ ] Responsive với Tailwind

## Ví dụ cấu trúc hoàn chỉnh

```
users/
├── page.tsx                    # export default UsersPage
├── _components/
│   ├── users-content.tsx      # Main content
│   ├── users-table.tsx        # Data table
│   ├── user-form.tsx          # Create/edit form
│   └── index.ts               # Export all
├── _hooks/
│   ├── use-users-data.ts      # Data management
│   ├── use-user-form.ts       # Form logic
│   └── index.ts               # Export all
├── _lib/
│   ├── api.ts                 # API functions
│   ├── validations.ts         # Zod schemas
│   └── utils.ts               # Helper functions
└── _types/
    └── index.ts               # User interfaces
```

Luôn nhớ: **Logic riêng, UI riêng, State không hard code!**
